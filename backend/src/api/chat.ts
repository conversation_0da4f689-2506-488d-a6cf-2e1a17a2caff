import { Router, Request, Response } from 'express';
import { ChatSessionModel } from '../database/models/ChatSession.js';
import { ConversationMessageModel } from '../database/models/ConversationMessage.js';
import { HumeEVIService } from '../hume/eviService.js';
import { authMiddleware } from '../middleware/auth.js';
import { ValidationError } from '../../../shared/types.js';
import type { ApiResponse, ChatSession, ConversationMessage } from '../../../shared/types.js';

const router = Router();
const humeService = new HumeEVIService();

// Apply authentication to all chat routes
router.use(authMiddleware.authenticate);

/**
 * GET /chat/sessions
 * Get user's chat sessions
 */
router.get('/sessions', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const sessions = await ChatSessionModel.findByUserId(userId, limit, offset);

    return res.json({
      success: true,
      data: { sessions },
      timestamp: new Date()
    } as ApiResponse<{ sessions: ChatSession[] }>);

  } catch (error) {
    console.error('Error fetching chat sessions:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SESSIONS_FETCH_ERROR',
        message: 'Failed to fetch chat sessions'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /chat/sessions/:sessionId
 * Get specific chat session details
 */
router.get('/sessions/:sessionId', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    const session = await ChatSessionModel.findById(sessionId);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Check if session belongs to user
    if (session.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'SESSION_ACCESS_DENIED',
          message: 'Access denied to this chat session'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Get session analytics
    const analytics = await humeService.getSessionAnalytics(sessionId);

    return res.json({
      success: true,
      data: analytics,
      timestamp: new Date()
    } as ApiResponse<typeof analytics>);

  } catch (error) {
    console.error('Error fetching chat session:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_FETCH_ERROR',
        message: 'Failed to fetch chat session'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /chat/sessions/:sessionId/messages
 * Get messages for a specific session
 */
router.get('/sessions/:sessionId/messages', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;
    const limit = parseInt(req.query.limit as string) || 100;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    // Verify session belongs to user
    const session = await ChatSessionModel.findById(sessionId);
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const messages = await ConversationMessageModel.findBySessionId(sessionId, limit, offset);

    return res.json({
      success: true,
      data: { messages },
      timestamp: new Date()
    } as ApiResponse<{ messages: ConversationMessage[] }>);

  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'MESSAGES_FETCH_ERROR',
        message: 'Failed to fetch chat messages'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /chat/sessions/:sessionId/transcript
 * Get transcript for a specific session
 */
router.get('/sessions/:sessionId/transcript', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    // Verify session belongs to user
    const session = await ChatSessionModel.findById(sessionId);
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const transcript = await ConversationMessageModel.generateTranscript(sessionId);

    return res.json({
      success: true,
      data: { transcript },
      timestamp: new Date()
    } as ApiResponse<{ transcript: string }>);

  } catch (error) {
    console.error('Error generating transcript:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'TRANSCRIPT_ERROR',
        message: 'Failed to generate transcript'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /chat/sessions/:sessionId/emotions
 * Get emotion analytics for a specific session
 */
router.get('/sessions/:sessionId/emotions', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    // Verify session belongs to user
    const session = await ChatSessionModel.findById(sessionId);
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const emotionAnalytics = await ConversationMessageModel.getEmotionAnalytics(sessionId);

    return res.json({
      success: true,
      data: emotionAnalytics,
      timestamp: new Date()
    } as ApiResponse<typeof emotionAnalytics>);

  } catch (error) {
    console.error('Error fetching emotion analytics:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'EMOTION_ANALYTICS_ERROR',
        message: 'Failed to fetch emotion analytics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * DELETE /chat/sessions/:sessionId
 * Delete a chat session and all its messages
 */
router.delete('/sessions/:sessionId', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    // Verify session belongs to user
    const session = await ChatSessionModel.findById(sessionId);
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Delete messages first (due to foreign key constraint)
    await ConversationMessageModel.deleteBySessionId(sessionId);
    
    // Delete session
    await ChatSessionModel.delete(sessionId);

    return res.json({
      success: true,
      data: { message: 'Chat session deleted successfully' },
      timestamp: new Date()
    } as ApiResponse<{ message: string }>);

  } catch (error) {
    console.error('Error deleting chat session:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SESSION_DELETE_ERROR',
        message: 'Failed to delete chat session'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /chat/analytics
 * Get user's overall chat analytics
 */
router.get('/analytics', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const days = parseInt(req.query.days as string) || 30;

    const analytics = await ChatSessionModel.getUserSessionSummary(userId, days);

    return res.json({
      success: true,
      data: analytics,
      timestamp: new Date()
    } as ApiResponse<typeof analytics>);

  } catch (error) {
    console.error('Error fetching chat analytics:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ANALYTICS_ERROR',
        message: 'Failed to fetch chat analytics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /chat/sessions/:sessionId/search
 * Search messages within a session
 */
router.post('/sessions/:sessionId/search', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId!;
    const { query: searchQuery, limit = 20 } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Session ID is required'
        },
        timestamp: new Date()
      });
    }

    if (!searchQuery) {
      throw new ValidationError('Search query is required');
    }

    // Verify session belongs to user
    const session = await ChatSessionModel.findById(sessionId);
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Chat session not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const messages = await ConversationMessageModel.searchMessages(sessionId, searchQuery, limit);

    return res.json({
      success: true,
      data: { messages, query: searchQuery },
      timestamp: new Date()
    } as ApiResponse<{ messages: ConversationMessage[]; query: string }>);

  } catch (error) {
    console.error('Error searching messages:', error);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        error: {
          code: (error as any).code || 'CHAT_ERROR',
          message: (error as Error).message
        },
        timestamp: new Date()
      } as ApiResponse);
    } else {
      return res.status(500).json({
        success: false,
        error: {
          code: 'SEARCH_ERROR',
          message: 'Failed to search messages'
        },
        timestamp: new Date()
      } as ApiResponse);
    }
  }
});

/**
 * POST /chat/test-hume-connection
 * Test Hume EVI connection (development only)
 */
router.post('/test-hume-connection', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;

    console.log(`🧪 Testing Hume connection for user: ${userId}`);

    // Create a test chat connection
    const humeService = new HumeEVIService();
    const { socket, session } = await humeService.createChatConnection(userId);

    console.log(`✅ Test connection created, session: ${session.id}`);

    // Close the connection after a short delay
    setTimeout(() => {
      socket.close();
      console.log(`🔌 Test connection closed`);
    }, 5000);

    return res.json({
      success: true,
      data: {
        sessionId: session.id,
        message: 'Test connection created successfully'
      },
      timestamp: new Date()
    } as ApiResponse<{ sessionId: string; message: string }>);

  } catch (error) {
    console.error('❌ Test connection failed:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'TEST_CONNECTION_ERROR',
        message: 'Failed to create test connection'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

export default router;
